<!DOCTYPE html>
<html lang="el">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Polis Chrysochous Museum</title>
  <link rel="stylesheet" href="css/style.css">
</head>

<body>
  <header>
    <div class="logo-title">
      <img src="images/logo.png" alt="Logo" class="logo">
      <h1>Nails</h1>
    </div>
  </header>

  <main>
    <!-- Photo viewer with navigation -->
    <div class="photo-viewer">
      <div class="photo-container">
        <img id="current-photo" src="images/13a.jpg" alt="Nails - View 1">
        <button id="prev-photo" class="nav-btn">❮</button>
        <button id="next-photo" class="nav-btn">❯</button>
        <div class="photo-nav">
          <span id="photo-counter">1 / 2</span>
        </div>
      </div>
    </div>

    <!-- Language toggle -->
    <div class="lang-switcher">
      <button id="langToggle">GR / EN</button>
    </div>

    <!-- Greek Description -->
    <div class="container lang lang-gr">
      <h2>Καρφίδες καθημερινής χρήσης</h2>
      <div class="section">
        <div class="section-title">1. Τύπος αντικειμένου:</div>
        <div class="section-content">Καρφίδες καθημερινής χρήσης</div>
      </div>
      <div class="section">
        <div class="section-title">2. Υλικά και τεχνικές:</div>
        <div class="section-content">
          Χάλκινα
        </div>
      </div>
      <div class="section">
        <div class="section-title">3. Χρονολόγηση:</div>
        <div class="section-content">Ελληνιστική περίοδος, 4ος-3ος αιώνας π.Χ.</div>
      </div>
      <div class="section">
        <div class="section-title">4. Περιοχή προέλευσης:</div>
        <div class="section-content">Μάριον.</div>
      </div>
      <div class="section">
        <div class="section-title">5. Περιγραφή:</div>
        <div class="section-content">
          α. Χάλκινη καρφίδα με πλατιά κυκλική κεφαλή και τετραγωνική διάμετρο προς την άκρη, όπως και κυκλική διατομή
          κάτω από
          την κεφαλή.
          <br>
          β. Χάλκινη καρφίδα με πλατιά κυκλική κεφαλή.
          <br>
          Πιστεύεται πως χρησιμοποιούνταν σε μεγάλα ξύλινα έπιπλα και λάρνακες. Οι καρφίδες κατασκευάστηκαν με τις
          τεχνικές
          χύτευσης και σφυρηλάτησης
        </div>
      </div>
    </div>

    <!-- English Description -->
    <div class="container lang lang-en" style="display: none;">
      <h2>Nails</h2>
      <div class="section">
        <div class="section-title">1. Type of object:</div>
        <div class="section-content">Nails</div>
      </div>
      <div class="section">
        <div class="section-title">2. Materials and techniques:</div>
        <div class="section-content">
          Bronze
        </div>
      </div>
      <div class="section">
        <div class="section-title">3. Date or period:</div>
        <div class="section-content">Hellenistic period, 4th - 3rd century BC</div>
      </div>
      <div class="section">
        <div class="section-title">4. Place of origin:</div>
        <div class="section-content">Marion</div>
      </div>
      <div class="section">
        <div class="section-title">5. Description:</div>
        <div class="section-content">
          a. Bronze nail with a wide circular head and a quadrangular (four-sided) shaft towards the point, where it
          becomes
          circular under the head.
          <br>
          b. Bronze nail with a wide circular head.
          <br>
          They were likely used in a large piece of furniture or coffin. They were crafted with the use of casting and
          hammering
          techniques.
        </div>
      </div>
    </div>

    <!-- 3D viewer -->
    <div id="container3D" data-model="Artefact13"></div>
  </main>

  <script>
    // Photo navigation
    const photos = [
      { src: 'images/13a.jpg', alt: 'Nails - View 1' },
      { src: 'images/13b.jpg', alt: 'Nails - View 2' }
    ];
    let currentPhotoIndex = 0;

    const currentPhoto = document.getElementById('current-photo');
    const photoCounter = document.getElementById('photo-counter');
    const prevBtn = document.getElementById('prev-photo');
    const nextBtn = document.getElementById('next-photo');

    function updatePhoto() {
      currentPhoto.src = photos[currentPhotoIndex].src;
      currentPhoto.alt = photos[currentPhotoIndex].alt;
      photoCounter.textContent = `${currentPhotoIndex + 1} / ${photos.length}`;
    }

    prevBtn.addEventListener('click', () => {
      currentPhotoIndex = (currentPhotoIndex - 1 + photos.length) % photos.length;
      updatePhoto();
    });

    nextBtn.addEventListener('click', () => {
      currentPhotoIndex = (currentPhotoIndex + 1) % photos.length;
      updatePhoto();
    });

    // Language toggle
    const langToggle = document.getElementById('langToggle');
    langToggle.addEventListener('click', () => {
      const gr = document.querySelector('.lang-gr');
      const en = document.querySelector('.lang-en');
      const isGR = gr.style.display !== 'none';
      gr.style.display = isGR ? 'none' : 'block';
      en.style.display = isGR ? 'block' : 'none';
    });
  </script>
</body>

</html>