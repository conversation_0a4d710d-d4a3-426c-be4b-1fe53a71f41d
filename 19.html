<!DOCTYPE html>
<html lang="el">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Polis Chrysochous Museum</title>
  <link rel="stylesheet" href="css/style.css">
</head>

<body>
  <header>
    <div class="logo-title">
      <img src="images/logo.png" alt="Logo" class="logo">
      <h1>Clay model of ship</h1>
    </div>
  </header>

  <main>
    <!-- Loading message -->
    <div id="loading">Loading 3D model...</div>

    <!-- 3D viewer -->
    <div id="container3D" data-model="Artefact19"></div>

    <!-- Language toggle -->
    <div class="lang-switcher">
      <button id="langToggle">GR / EN</button>
    </div>

    <!-- Greek Description -->
    <div class="container lang lang-gr">
      <h2>Πήλινο ομοίωμα πλοίου</h2>
      <div class="section">
        <div class="section-title">1. Τύπος αντικειμένου:</div>
        <div class="section-content">Πήλινο ομοίωμα πλοίου</div>
      </div>
      <div class="section">
        <div class="section-title">2. Υλικά και τεχνικές:</div>
        <div class="section-content">
          Πηλός
        </div>
      </div>
      <div class="section">
        <div class="section-title">3. Χρονολόγηση:</div>
        <div class="section-content">Κυπρο-Αρχαϊκή (750-475 π.Χ.)</div>
      </div>
      <div class="section">
        <div class="section-title">4. Περιοχή εντοπισμού:</div>
        <div class="section-content">Λύση, επαρχία Αμμοχώστου</div>
      </div>
      <div class="section">
        <div class="section-title">5. Περιγραφή:</div>
        <div class="section-content">
          Το πήλινο ομοίωμα πλοίου παρουσιάζει ένα μακρόστενο, λεπτό κύτος με απολήξεις σε σχήμα κανό. Η μία πλώρη
          προεξέχει κατά
          1 εκ. αλλά είναι φθαρμένη, ενώ η άλλη υψώνεται κατά 2,5 εκ. πάνω από την επιγκενίδα (κουπαστή). Αχνές
          κατακόρυφες και
          παράλληλες γραμμές είναι ορατές και στις δύο πλευρές του ομοιώματος.
          <br>
          Υπάρχει η υπόθεση ότι αυτές οι γραμμές ενδέχεται να υποδηλώνουν μια συγκεκριμένη τεχνική ναυπηγικής—πιθανώς
          δέρμα
          τεντωμένο πάνω σε ξύλινα πλαίσια, μια μέθοδο, που είναι ιστορικά γνωστή από τις περιοχές του Ευφράτη και του
          Τίγρη.
          Ωστόσο, είναι επίσης πιθανό αυτές οι γραμμές να είναι απλώς αποτυπώματα δακτύλων που άφησε ο τεχνίτης κατά την
          κατασκευή
          του ομοιώματος.
        </div>
      </div>
    </div>

    <!-- English Description -->
    <div class="container lang lang-en" style="display: none;">
      <h2>Clay model of ship</h2>
      <div class="section">
        <div class="section-title">1. Type of object:</div>
        <div class="section-content">Clay model of ship</div>
      </div>
      <div class="section">
        <div class="section-title">2. Materials and techniques:</div>
        <div class="section-content">
          Red clay, no painted decoration
        </div>
      </div>
      <div class="section">
        <div class="section-title">3. Date or period:</div>
        <div class="section-content">Cypro-Archaic (750-475BC)</div>
      </div>
      <div class="section">
        <div class="section-title">4. Place of discovery:</div>
        <div class="section-content">Lysi, Famagusta district</div>
      </div>
      <div class="section">
        <div class="section-title">5. Description:</div>
        <div class="section-content">
          The clay boat features a long, slender hull with canoe-shaped stems. One stem protrudes 1 cm but is damaged,
          while the
          other rises 2.5 cm above the gunwale. Faint vertical and parallel lines are visible on both sides of the
          model.
          <br>
          There is a hypothesis that these markings indicate a specific shipbuilding technique—possibly hide stretched
          over wooden
          frames, a method historically known from the Euphrates and Tigris regions. However, it is also likely that
          these lines
          are simply fingerprints left during the model's creation.
        </div>
      </div>
    </div>
  </main>

  <script type="module" src="js/main.js"></script>
  <script>
    const langToggle = document.getElementById('langToggle');
    langToggle.addEventListener('click', () => {
      const gr = document.querySelector('.lang-gr');
      const en = document.querySelector('.lang-en');
      const isGR = gr.style.display !== 'none';
      gr.style.display = isGR ? 'none' : 'block';
      en.style.display = isGR ? 'block' : 'none';
    });
  </script>
</body>

</html>