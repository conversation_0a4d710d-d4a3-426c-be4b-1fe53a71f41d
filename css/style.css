/* Reset & global styles */
body, html {
  margin: 0;
  padding: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f9f9f9;
  color: #333;
  height: 100%;
  position: relative;
}

/* Header */
header {
  background-color: #4A90E2;
  color: white;
  padding: 20px 0;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

h1 {
  margin: 0;
  font-size: 1.8rem;
}

/* Main layout */
main {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 20px;
}

/* Loading overlay */
#loading {
  position: absolute;
  top: 140px;
  font-size: 1.3rem;
  background: rgba(255, 255, 255, 0.95);
  padding: 15px 30px;
  border-radius: 10px;
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.15);
  z-index: 10;
}

/* 3D viewer container */
#container3D {
  width: 80%;
  max-width: 800px;
  height: 500px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

/* Language toggle */
.lang-switcher {
  width: 80%;
  max-width: 800px;
  margin: 0 auto 20px auto;
  text-align: right;
}

#langToggle {
  background-color: #4A90E2;
  color: white;
  border: none;
  padding: 8px 14px;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background 0.2s ease-in-out;
}

#langToggle:hover {
  background-color: #357abd;
}

/* Description container */
.container {
  width: 80%;
  max-width: 800px;
  background: white;
  border-radius: 12px;
  padding: 30px 40px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

/* Typography */
h2 {
  color: #4A90E2;
  margin-top: 0;
}

.section {
  margin-bottom: 20px;
}

.section-title {
  font-weight: bold;
  margin-bottom: 5px;
  color: #444;
}

.section-content {
  line-height: 1.6;
}

.logo-title {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 20px;
  gap: 15px;
}

.logo {
  height: 40px;
  width: auto;
  object-fit: contain;
}

/* Photo viewer styles */
.photo-viewer,
#photo-viewer {
  width: 80%;
  max-width: 800px;
  height: 500px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
}

.photo-container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.photo-container img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  display: block;
}

/* Navigation arrows positioned on left and right of container */
.photo-viewer .nav-btn,
#photo-viewer .nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.6);
  border: none;
  color: white;
  font-size: 24px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  z-index: 10;
  transition: background 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.photo-viewer .nav-btn:hover,
#photo-viewer .nav-btn:hover {
  background: rgba(0, 0, 0, 0.8);
}

.photo-viewer #prev-photo,
#photo-viewer #prev-photo {
  left: 20px;
}

.photo-viewer #next-photo,
#photo-viewer #next-photo {
  right: 20px;
}

/* Photo counter at bottom center */
.photo-viewer .photo-nav,
#photo-viewer .photo-nav {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  padding: 8px 16px;
  border-radius: 20px;
  color: white;
  font-size: 14px;
  z-index: 10;
}

.photo-viewer #photo-counter,
#photo-viewer #photo-counter {
  font-size: 14px;
  color: white;
}
