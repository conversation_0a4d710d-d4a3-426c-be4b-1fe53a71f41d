<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Polis Museum Models</title>
    <link rel="stylesheet" href="css/style.css" />
    <style>
      body {
          font-family: Arial, sans-serif;
          text-align: center;
          margin: 20px;
          color: #a6a6a6;
          background-color: #4d4d4d;
      }
      h1, h2 {
          color: #c4c4c4;
      }
      ul {
          list-style-type: none;
          padding: 0;
      }
      li {
          margin: 10px 0;
          font-size: 18px;
      }
      button {
          background-color: #4CAF50; /* Green background */
          color: white; /* White text */
          border: none;
          border-radius: 5px; /* Rounded corners */
          padding: 10px 20px;
          font-size: 16px;
          cursor: pointer;
          transition: all 0.3s ease; /* Smooth hover effect */
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Slight shadow */
          margin-left: 20px;
      }
      button:hover {
          background-color: #45a049; /* Darker green on hover */
          transform: translateY(-2px); /* Lift effect */
      }
      button:active {
          transform: translateY(1px); /* Slight press effect */
      }
      button + button {
          margin-left: 10px; /* Spacing between buttons */
      }
  </style>
</head>
<body>
    <h1>Statues</h1>
    <ul>
        <li>Athena
          <button onclick="redirectTo('athena.html')">View</button>
        </li>
        <li>Poseidon
          <button onclick="redirectTo('poseidon.html')">View</button>
        </li>
        <li>Aphrodite
          <button onclick="redirectTo('aphrodite.html')">View</button>
        </li>
    </ul>

    <h2>Actions</h2>

    <script>
        function redirectTo(page) {
            window.location.href = page;
        }
    </script>
</body>
</html>
