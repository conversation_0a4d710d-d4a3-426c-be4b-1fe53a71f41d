import * as THREE from "https://cdn.skypack.dev/three@0.129.0/build/three.module.js";
import { OrbitControls } from "https://cdn.skypack.dev/three@0.129.0/examples/jsm/controls/OrbitControls.js";
import { GLTFLoader } from "https://cdn.skypack.dev/three@0.129.0/examples/jsm/loaders/GLTFLoader.js";

// DOM references
const container = document.getElementById("container3D");
const loading = document.getElementById("loading");
const objToRender = container.dataset.model;

// Scene
const scene = new THREE.Scene();

// Camera (more zoomed-out position for better object view)
const camera = new THREE.PerspectiveCamera(
  75,
  container.clientWidth / container.clientHeight,
  0.1,
  1000
);
camera.position.set(0, 30, 1000); // more zoomed out for better view
camera.lookAt(0, 0, 0);

// Renderer
const renderer = new THREE.WebGLRenderer({ alpha: true, antialias: true });
renderer.setSize(container.clientWidth, container.clientHeight);
container.appendChild(renderer.domElement);

// Lights
const topLight = new THREE.DirectionalLight(0xffffff, 5);

const ambientLight = new THREE.AmbientLight(0xffffff, 1.5);
scene.add(ambientLight);

topLight.position.set(0, 100, 0);
scene.add(topLight);

const offset = 50;
for (let x = 0; x < 3; x++) {
  for (let y = 0; y < 3; y++) {
    for (let z = 0; z < 3; z++) {
      const pointLight = new THREE.PointLight(0xffffff, 2, 100);
      pointLight.position.set(
        x * offset - offset,
        y * offset - offset,
        z * offset - offset
      );
      scene.add(pointLight);
    }
  }
}

// Controls
const controls = new OrbitControls(camera, renderer.domElement);
controls.target.set(0, 0, 0);
controls.update();

// Load model with corrected path
const loader = new GLTFLoader();
loader.load(
  `./models/${objToRender}.glb`, // ← fixed path
  function (gltf) {
    const object = gltf.scene;
    scene.add(object);
    loading.style.display = "none";
  },
  function (xhr) {
    console.log((xhr.loaded / xhr.total) * 100 + "% loaded");
  },
  function (error) {
    console.error("Error loading model:", error);
    loading.textContent = "Failed to load model.";
  }
);

// Resize
window.addEventListener("resize", () => {
  camera.aspect = container.clientWidth / container.clientHeight;
  camera.updateProjectionMatrix();
  renderer.setSize(container.clientWidth, container.clientHeight);
});

// Animation loop
function animate() {
  requestAnimationFrame(animate);
  renderer.render(scene, camera);
}
animate();
